export const MessageType = {
  START_TRANSLATE: 'START_TRANSLATE', // 开始翻译
  CREATE_CONVERSATION: 'CREATE_CONVERSATION', // 创建会话
  BATCH_TRANSLATE: 'BATCH_TRANSLATE', // 批量翻译
  SINGLE_TRANSLATE: 'SINGLE_TRANSLATE', // 单个翻译
  CHECK_CONTENT_SCRIPT: 'CHECK_CONTENT_SCRIPT', // 检查content_script是否加载
}

export interface TMessage<T> {
  type: string
  data?: T
}

/**
 * 插件用到的所有服务
 */
const API_GATE_WAY =
  'http://************:8090/sourcemap-analyzer/htsc-ai-extension' // 测试环境api网关

export const SERVERS_CONFIG = {
  LOGIN_PRD: `${API_GATE_WAY}/gw-server/prd`,
  LOGIN_DEV: `${API_GATE_WAY}/gw-server/dev`,
  AGENT_TRANSLATE: `${API_GATE_WAY}/agent-server/translate`,
  CI_SERVER: `${API_GATE_WAY}/ci-server`,
}
/**
 * EIP 登录相关配置
 */
export const EIP_CONFIG = {
  BASE_URL: 'http://eipuat.htsc.com.cn',
  LOGIN_PAGE: 'http://eipuat.htsc.com.cn',
  LOGIN_API: 'http://eipuat.htsc.com.cn/gateway/login',
  LOGOUT_API: 'http://eipuat.htsc.com.cn/gateway/logout',
  AUTH_API: 'http://eipuat.htsc.com.cn/portal-xc/auth/loginXC',
  TOKEN_NAME: 'EIPGW-TOKEN',
}

/**
 * 悬浮球操作类型
 */
export enum EFloatButtonActionType {
  Translate = '翻译',
  Screenshot = '截图',
  Summary = '总结',
  OpenPanel = '打开侧边栏面板',
}

/**
 * contents中的消息类型
 */
export enum EContentsMessageType {
  SelectionBar = 'SelectionBar',
  FloatingButton = 'FloatingButton',
}
