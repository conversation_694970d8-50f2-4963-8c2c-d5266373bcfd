import { useEffect } from 'react'
import { message } from 'antd/es'
import { MessageType } from '@src/common/const'
import { ipConnectivity } from '@src/common/utils'

const useTranslate = (chatUiRef) => {
  const [messageApi, contextHolder] = message.useMessage()

  const getUserId = () => {
    // 由于移除了登录功能，这里返回一个默认用户ID
    return "anonymous_user"
  }

  // 创建会话
  const createConversation = async (sendResponse) => {
    try {
      const response = await fetch(
        `http://*************:9607/ai/orchestration/session/createSession`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userID: getUserId(),
            appId: "web-assistant"
          }),
        }
      )

      const res = await response.json()
      const conversationID = res.resultData.conversationId
      console.log('res:', res);


      if (conversationID) {
        sendResponse({
          code: '0',
          conversationID: conversationID,
        })
      } else {
        sendResponse({
          code: '1',
          error: '会话创建失败',
        })
      }
    } catch (error) {
      console.log('创建会话错误:', error)
      sendResponse({
        code: '1',
        error: error.message,
      })
    }
  }

  // 处理翻译请求
  const handleTranslate = async (query: string, groupId: string, isBatch: boolean = false) => {
    console.log('chatUiRef', chatUiRef, chatUiRef.current.chatContext.onSend);

    try {
      const response = await chatUiRef.current.chatContext.onSend('text', `翻译此页面：${query}`, {
        agentId: 'translate'
      })
      console.log('翻译响应:', response);

      // 处理翻译结果
      if (response && response.endFlag) {
        let translationResult = ''

        // 处理 response.list 数据结构
        if (response.list && Array.isArray(response.list)) {
          // 遍历 response.list 数组，提取每个对象的 content.text 字段
          const extractedTexts = response.list
            .filter((item: any) => item.content && item.content.text)
            .map((item: any) => item.content.text);

          // 将所有提取的文本拼接成一个完整的字符串
          translationResult = extractedTexts.join('');
        } else if (response.result) {
          translationResult = response.result
        }

        // 使用正则表达式提取翻译结果
        const regex = /翻译结果：(.*?)(?=[^\\]")/;
        const match = translationResult.match(regex);
        if (match) {
          translationResult = match[1].trim();
        }

        // 处理翻译结果并触发自定义事件
        let results: string | string[]
        if (isBatch) {
          // 批量翻译：按 ### 分割结果
          results = translationResult.split('###')
        } else {
          // 单个翻译：直接使用结果
          results = translationResult
        }

       
      }
    } catch (error) {
      console.error('翻译处理错误:', error)
    }


    // try {
    //   const chatResponse = await fetch(
    //     `http://webassist.sit.sass.htsc/chat/workflow/chrome`,
    //     {
    //       method: 'POST',
    //       headers: {
    //         'Content-Type': 'application/json',
    //       },
    //       body: JSON.stringify({
    //         appId: 'web-assistant',
    //         userID: getUserId(),
    //         query: query,
    //         conversationId: conversationID,
    //         // ResponseMode: 'blocking',
    //         agentId: 'translate'
    //       }),
    //     }
    //   )
    //   const chatRes = await chatResponse.text()
    //   console.log('翻译响应:', {
    //     query,
    //     chatRes
    //   })

    //   try {
    //     // 参考Java代码的处理方式
    //     const lines = chatRes.split('\n');
    //     if (lines.length > 1) {
    //       // 假设第二行包含我们需要的数据
    //       const dataLine = lines[1];
    //       // 假设数据以某个前缀开始
    //       const prefix = 'data:data: ';
    //       if (dataLine.startsWith(prefix)) {
    //         const jsonStr = dataLine.substring(prefix.length);
    //         const jsonObj = JSON.parse(jsonStr);
    //         if (jsonObj && jsonObj.answer) {
    //           console.log('jsonStr:', jsonStr, jsonObj.answer);

    //           sendResponse({
    //             code: '0',
    //             result: jsonObj.answer.replace('翻译结果：', '').trim(),
    //           });
    //           return;
    //         }
    //       }
    //     }

    //     // 如果上面的处理失败，尝试使用正则表达式
    //     const regex = /翻译结果：(.*?)(?=[^\\]")/;
    //     const match = chatRes.match(regex);
    //     if (match) {
    //       const translation = match[1].trim();
    //       sendResponse({
    //         code: '0',
    //         result: translation,
    //       });
    //     } else {
    //       sendResponse({
    //         code: '1',
    //         error: '翻译结果解析失败',
    //       });
    //     }
    //   } catch (parseError) {
    //     console.log('解析翻译结果错误:', parseError);
    //     sendResponse({
    //       code: '1',
    //       error: '翻译结果解析失败: ' + parseError.message,
    //     });
    //   }
    // } catch (error) {
    //   console.log('翻译错误:', error)
    //   sendResponse({
    //     code: '1',
    //     error: error.message,
    //   })
    // }
  }

  useEffect(() => {
    ipConnectivity(messageApi)
    const messageHandler = (message: any, _sender: any, sendResponse: any) => {
      if (message.type === MessageType.CREATE_CONVERSATION) {
        createConversation(sendResponse)
      } else if (message.type === MessageType.BATCH_TRANSLATE) {
        const { query, conversationID, groupId } = message.data
        handleTranslate(query, groupId, true) // 批量翻译
      } else if (message.type === MessageType.SINGLE_TRANSLATE) {
        const { query, conversationID, groupId } = message.data
        handleTranslate(query, groupId, false) // 单个翻译
      }
      return true
    }

    chrome.runtime.onMessage.addListener(messageHandler)

    // 清理函数
    return () => {
      chrome.runtime.onMessage.removeListener(messageHandler)
    }
  }, [])

  return contextHolder
}

export default useTranslate