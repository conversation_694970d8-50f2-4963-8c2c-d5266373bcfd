/**
 * 翻译相关的Action函数
 */

import { updateIconWithTranslation } from '@src/contents/scripts/injectTranslate';
import type { BaseAction } from './index'

/**
 * 处理翻译API返回值的action函数
 * 这个函数封装了原本在批量翻译处理中的响应处理逻辑
 */
export const createInsertDomAction = (): BaseAction => {
  return {
    name: 'insertDom',
    description: '处理翻译API返回值，更新页面翻译图标',
    handler: (response: any, group: any[]) => {
      console.log('handler', response);
      if (response.endFlag) {
        // 处理 response.list 数据结构
        if (response.list && Array.isArray(response.list)) {
          // 遍历 response.list 数组，提取每个对象的 content.text 字段
          const extractedTexts = response.list
            .filter((item: any) => item.content && item.content.text)
            .map((item: any) => item.content.text);

          // 将所有提取的文本拼接成一个完整的字符串
          const concatenatedText = extractedTexts.join('');

          // 如果成功提取到文本，则使用拼接后的字符串替换 response.result
          if (concatenatedText) {
            response.result = concatenatedText;
          }
        }

        // 使用'###'分割批量翻译结果
        const regex = /翻译结果：(.*?)(?=[^\\]")/;
        const match = response.result.match(regex);
        if (match) {
          const results = response.result.split('###');

          group.forEach((item: any, index: number) => {
            if (results[index]) {
              updateIconWithTranslation(
                item.icon,
                results[index],
                item.paragraph,
                item.htmlStructure
              )
            }
          });
        }
        else {
          // 处理错误情况
          console.error('翻译失败:', response?.error || '未知错误');
        }
      }


    }
  };
};

/**
 * 获取所有翻译相关的actions
 */
export const getTranslationActions = (): BaseAction[] => {
  return [
    createInsertDomAction()
  ];
};